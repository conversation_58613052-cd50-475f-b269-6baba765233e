<?php
session_start(); // Start the session at the very beginning

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header("Location: index.php"); // Redirect to your main dashboard
    exit();
}

ini_set('display_errors', 0); // Don't display errors directly to users on login page
ini_set('log_errors', 1);    // Log errors
error_reporting(E_ALL);

require_once 'config/database.php'; // Include your database configuration
require_once 'log_helper.php'; // Include log helper functions

$error_message = '';
$username_value = ''; // To repopulate username/email field on error

// Check database connection AFTER including the config
if (!$conn) {
    error_log("Database connection failed in login.php: " . mysqli_connect_error());
    $error_message = "System error. Please try again later.";
} else {
    mysqli_set_charset($conn, "utf8mb4"); // Ensure UTF-8

    // --- Handle Form Submission ---
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        $username = isset($_POST['username']) ? trim($_POST['username']) : ''; // This can be either username or email
        $password = isset($_POST['password']) ? $_POST['password'] : '';
        $username_value = htmlspecialchars($username); // Store for repopulating the form

        if (empty($username) || empty($password)) {
            $error_message = "Please enter both username/email and password.";
        } else {
            // Prepare Statement to prevent SQL Injection
            // Modified to check for either username or email
            $sql = "SELECT user_id, username, password, full_name, email, profile_picture, role, status, bureau
                    FROM tblusers
                    WHERE username = ? OR email = ?
                    LIMIT 1";
            $stmt = mysqli_prepare($conn, $sql);

            if ($stmt) {
                mysqli_stmt_bind_param($stmt, "ss", $username, $username);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);

                if ($result && mysqli_num_rows($result) === 1) {
                    $user = mysqli_fetch_assoc($result);

                    // Verify Password
                    if (password_verify($password, $user['password'])) {
                        // Check User Status
                        if (strtolower($user['status']) === 'active') {
                            // Login Successful
                            session_regenerate_id(true); // Prevent session fixation

                            // Store essential user data in session
                            $_SESSION['user_id'] = $user['user_id'];
                            $_SESSION['username'] = $user['username'];
                            $_SESSION['full_name'] = $user['full_name'];
                            $_SESSION['role'] = $user['role'];
                            $_SESSION['profile_picture'] = $user['profile_picture'];
                            $_SESSION['bureau'] = $user['bureau']; // Store bureau for access control
                            $_SESSION['login_time'] = time();

                            // Update Last Login Time
                            $update_sql = "UPDATE tblusers SET last_login = NOW() WHERE user_id = ?";
                            $update_stmt = mysqli_prepare($conn, $update_sql);
                            if ($update_stmt) {
                                mysqli_stmt_bind_param($update_stmt, "i", $user['user_id']);
                                if (!mysqli_stmt_execute($update_stmt)) {
                                     error_log("Failed to update last_login for user_id: " . $user['user_id'] . " - Error: " . mysqli_stmt_error($update_stmt));
                                }
                                mysqli_stmt_close($update_stmt);
                            } else {
                                error_log("Failed to prepare last_login update statement: " . mysqli_error($conn));
                            }

                            // Log successful login
                            $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
                            $details = "Login from IP: " . $ip;
                            add_log_entry(
                                $user['user_id'],
                                $user['username'],
                                "User logged in successfully",
                                "login",
                                null,
                                "user",
                                $details
                            );

                            // Redirect to Dashboard
                            header("Location: index.php");
                            exit();
                        } else {
                            error_log("Login attempt failed for inactive user: " . $username);
                            $error_message = "Your account is inactive. Please contact support.";
                        }
                    } else {
                        error_log("Login attempt failed (invalid password) for user: " . $username);
                        $error_message = "Invalid username/email or password.";
                    }
                } else {
                    error_log("Login attempt failed (user not found): " . $username);
                    $error_message = "Invalid username/email or password.";
                }
                mysqli_free_result($result);
                mysqli_stmt_close($stmt);
            } else {
                error_log("Login statement preparation failed: " . mysqli_error($conn));
                $error_message = "Login error. Please try again later.";
            }
        }
    } // End POST request handling
} // End DB connection check

// Close DB connection if it was opened
if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
    mysqli_close($conn);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Activity Monitoring</title>
    <!-- Link to your existing stylesheet -->
    <link rel="stylesheet" href="css/style.css">
    <!-- Link to Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Inherit variables from style.css */
        :root {
             /* Define fallback variables ONLY if style.css might not load,
                otherwise rely on the main stylesheet. */
            --bg-color: #F7F8FC;
            --bg-light: #FFFFFF;
            --border-color: #EAEAEA;
            --border-radius: 16px;
            --primary-color: #6A5AE0;
            --secondary-color: #4A4A6A;
            --text-light: #9a9a9a;
            --red-color: #E57373;
            --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            --font-family: 'Poppins', sans-serif;
        }

        /* Login specific styles */
        body.login-page {
            /* Advanced futuristic background with 3D vibes */
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a3e 25%, #2d2d5f 50%, #1a1a3e 75%, #0f0f23 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;

            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: var(--font-family);
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 3D Floating Elements */
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        /* WiFi Signal Animations */
        .wifi-signal {
            position: absolute;
            color: rgba(106, 90, 224, 0.3);
            font-size: 2rem;
            animation: floatWifi 8s ease-in-out infinite;
        }

        .wifi-signal:nth-child(1) {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
            transform: rotate(-15deg);
        }

        .wifi-signal:nth-child(2) {
            top: 25%;
            right: 15%;
            animation-delay: 2s;
            transform: rotate(20deg);
        }

        .wifi-signal:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
            transform: rotate(-10deg);
        }

        .wifi-signal:nth-child(4) {
            bottom: 30%;
            right: 10%;
            animation-delay: 6s;
            transform: rotate(25deg);
        }

        @keyframes floatWifi {
            0%, 100% {
                transform: translateY(0px) rotate(var(--rotation, 0deg)) scale(1);
                opacity: 0.3;
            }
            25% {
                transform: translateY(-20px) rotate(calc(var(--rotation, 0deg) + 5deg)) scale(1.1);
                opacity: 0.5;
            }
            50% {
                transform: translateY(-10px) rotate(var(--rotation, 0deg)) scale(1.05);
                opacity: 0.4;
            }
            75% {
                transform: translateY(-30px) rotate(calc(var(--rotation, 0deg) - 5deg)) scale(0.95);
                opacity: 0.6;
            }
        }

        /* User Icons */
        .user-icon {
            position: absolute;
            color: rgba(255, 255, 255, 0.2);
            font-size: 1.5rem;
            animation: floatUser 10s ease-in-out infinite;
        }

        .user-icon:nth-child(5) {
            top: 10%;
            left: 25%;
            animation-delay: 1s;
        }

        .user-icon:nth-child(6) {
            top: 60%;
            right: 25%;
            animation-delay: 3s;
        }

        .user-icon:nth-child(7) {
            bottom: 15%;
            left: 40%;
            animation-delay: 5s;
        }

        @keyframes floatUser {
            0%, 100% {
                transform: translateY(0px) scale(1);
                opacity: 0.2;
            }
            33% {
                transform: translateY(-15px) scale(1.1);
                opacity: 0.4;
            }
            66% {
                transform: translateY(-25px) scale(0.9);
                opacity: 0.3;
            }
        }

        /* Birds Animation */
        .bird {
            position: absolute;
            color: rgba(255, 255, 255, 0.15);
            font-size: 1.2rem;
            animation: flyBird 12s linear infinite;
        }

        .bird:nth-child(8) {
            top: 20%;
            left: -50px;
            animation-delay: 0s;
        }

        .bird:nth-child(9) {
            top: 40%;
            left: -50px;
            animation-delay: 4s;
        }

        .bird:nth-child(10) {
            top: 70%;
            left: -50px;
            animation-delay: 8s;
        }

        @keyframes flyBird {
            0% {
                left: -50px;
                transform: translateY(0px) rotate(0deg);
            }
            25% {
                transform: translateY(-10px) rotate(5deg);
            }
            50% {
                transform: translateY(5px) rotate(-3deg);
            }
            75% {
                transform: translateY(-5px) rotate(2deg);
            }
            100% {
                left: calc(100% + 50px);
                transform: translateY(0px) rotate(0deg);
            }
        }

        /* Geometric Shapes */
        .geometric-shape {
            position: absolute;
            border: 2px solid rgba(106, 90, 224, 0.1);
            animation: rotateShape 20s linear infinite;
        }

        .shape-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            top: 30%;
            right: 5%;
            animation-delay: 0s;
        }

        .shape-triangle {
            width: 0;
            height: 0;
            border-left: 30px solid transparent;
            border-right: 30px solid transparent;
            border-bottom: 50px solid rgba(106, 90, 224, 0.1);
            border-radius: 0;
            top: 50%;
            left: 5%;
            animation-delay: 5s;
        }

        .shape-square {
            width: 60px;
            height: 60px;
            top: 80%;
            right: 20%;
            animation-delay: 10s;
            transform: rotate(45deg);
        }

        @keyframes rotateShape {
            0% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(90deg) scale(1.1); }
            50% { transform: rotate(180deg) scale(0.9); }
            75% { transform: rotate(270deg) scale(1.05); }
            100% { transform: rotate(360deg) scale(1); }
        }

        .login-container {
            /* Glassmorphism effect with 3D vibes */
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);

            padding: 40px 35px;
            border-radius: var(--border-radius);

            /* Enhanced 3D shadow effects */
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.3),
                0 15px 35px rgba(106, 90, 224, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);

            width: 100%;
            max-width: 420px;
            text-align: center;
            position: relative;
            z-index: 10;

            /* 3D transform and animations */
            transform: perspective(1000px) rotateX(2deg) rotateY(-2deg);
            animation: fadeInFloat 1s ease-out, containerFloat 6s ease-in-out infinite 1s;
            transition: transform 0.3s ease;
        }

        .login-container:hover {
            transform: perspective(1000px) rotateX(0deg) rotateY(0deg) translateY(-5px);
            box-shadow:
                0 35px 70px rgba(0, 0, 0, 0.4),
                0 20px 45px rgba(106, 90, 224, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        @keyframes fadeInFloat {
            from {
                opacity: 0;
                transform: perspective(1000px) rotateX(10deg) rotateY(-10deg) translateY(50px);
            }
            to {
                opacity: 1;
                transform: perspective(1000px) rotateX(2deg) rotateY(-2deg) translateY(0px);
            }
        }

        @keyframes containerFloat {
            0%, 100% {
                transform: perspective(1000px) rotateX(2deg) rotateY(-2deg) translateY(0px);
            }
            50% {
                transform: perspective(1000px) rotateX(1deg) rotateY(-1deg) translateY(-3px);
            }
        }


        .login-logo {
            margin-bottom: 30px; /* Increased space below logo */
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
        }

        .login-logo img {
            height: 85px; /* Standard logo size - increased from 70px */
            width: auto;
        }

        .login-title {
            font-size: 19px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 8px;
            line-height: 1.3;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .login-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 30px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }


        .login-form .form-group {
            margin-bottom: 22px; /* Slightly more space between fields */
            text-align: left;
        }

        .login-form label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* Style for the input group (icon + input) */
        .input-group {
            position: relative;
        }

        .input-group .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            pointer-events: none;
            font-size: 16px; /* Slightly larger icon */
            transition: color 0.2s ease;
        }

        .input-group .input-icon-right {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            pointer-events: none;
            font-size: 16px;
            transition: color 0.2s ease;
        }

        /* Password toggle button */
        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            cursor: pointer;
            font-size: 16px;
            transition: color 0.2s ease;
            background: none;
            border: none;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        /* Change icon color on input focus for visual feedback */
        .login-form .input-group input:focus + .input-icon {
            color: var(--primary-color);
        }

        .login-form .input-group input[type="text"],
        .login-form .input-group input[type="password"] {
            width: 100%;
            padding: 12px 15px 12px 45px;
            padding-right: 45px;

            /* Glassmorphism input styling */
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius);

            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-family: var(--font-family);

            transition: all 0.3s ease;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Reset token input with right icon */
        #reset_token {
            padding: 12px 45px 12px 15px; /* Padding for right icon */
        }

        .login-form .input-group input[type="text"]:focus,
        .login-form .input-group input[type="password"]:focus {
            outline: none;
            border-color: rgba(106, 90, 224, 0.6);
            background: rgba(255, 255, 255, 0.15);
            box-shadow:
                0 0 0 3px rgba(106, 90, 224, 0.2),
                inset 0 2px 4px rgba(0, 0, 0, 0.1),
                0 4px 12px rgba(106, 90, 224, 0.1);
            transform: translateY(-1px);
        }

        /* Ensure consistent styling when toggling password visibility */
        .login-form .input-group input {
            font-size: 14px !important;
            font-family: var(--font-family) !important;
        }

         /* Style placeholder text */
        .login-form input::placeholder {
            color: rgba(255, 255, 255, 0.5);
            opacity: 1;
        }

        .login-button {
            width: 100%;
            padding: 13px 15px; /* Slightly taller button */
            font-size: 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: background-color 0.2s ease, transform 0.1s ease;
            font-weight: 600;
            margin-top: 15px; /* Increased space above button */
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .login-button:hover {
            background-color: #5a4bd3; /* Darken primary color on hover */
        }
         .login-button:active {
             transform: translateY(1px); /* Add press effect */
         }


        .error-message {
            background-color: rgba(229, 115, 115, 0.1);
            color: var(--red-color);
            border: 1px solid rgba(229, 115, 115, 0.3);
            border-radius: var(--border-radius);
            padding: 12px 15px;
            margin-bottom: 20px;
            font-size: 14px;
            text-align: center;
            display: <?php echo empty($error_message) ? 'none' : 'block'; ?>;
            animation: shake 0.4s ease-in-out; /* Add shake animation on error */
        }
        .error-message i {
            margin-right: 8px;
        }

        /* Simple shake animation */
        @keyframes shake {
          10%, 90% { transform: translateX(-1px); }
          20%, 80% { transform: translateX(2px); }
          30%, 50%, 70% { transform: translateX(-3px); }
          40%, 60% { transform: translateX(3px); }
        }

        .login-footer {
            margin-top: 30px; /* Increased space */
            font-size: 13px;
            color: var(--text-light);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        .login-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        .login-footer a:hover {
            text-decoration: underline;
        }
        .login-footer .copyright {
            text-align: center;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
            animation: fadeIn 0.2s ease-out;
        }

        .modal.small .modal-content {
            max-width: 450px;
        }

        .modal-content {
            background-color: var(--bg-light);
            margin: 10vh auto;
            padding: 0;
            border-radius: var(--border-radius);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
            width: 90%;
            max-width: 600px;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from { transform: translateY(-30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 18px;
            color: var(--secondary-color);
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 22px;
            font-weight: bold;
            color: var(--text-light);
            cursor: pointer;
            padding: 0;
            line-height: 1;
        }

        .close-modal:hover {
            color: var(--secondary-color);
        }

        .modal-body {
            padding: 20px;
        }

        .modal-body p {
            margin-top: 0;
            margin-bottom: 20px;
            color: var(--secondary-color);
            font-size: 14px;
        }

        /* Responsive Adjustments */
        @media (max-width: 480px) {
            .login-container {
                padding: 30px 25px;
                max-width: 90%;
            }
            .login-logo img {
                height: 75px; /* Proportionally increased for mobile */
            }
            .login-title {
                font-size: 18px;
            }
            .login-subtitle {
                 margin-bottom: 25px;
            }
            .login-form .input-group input[type="text"],
            .login-form .input-group input[type="password"] {
                 padding: 11px 15px 11px 40px; /* Adjust padding */
                 padding-right: 40px; /* Add right padding for the toggle button */
            }
             .input-group .input-icon {
                 font-size: 15px;
                 left: 13px;
            }

            .input-group .input-icon-right {
                font-size: 15px;
                right: 13px;
            }

            .password-toggle {
                right: 13px;
                font-size: 15px;
            }

            .login-button {
                 padding: 12px 15px;
            }
        }

    </style>
</head>
<body class="login-page">

    <div class="login-container">
        <div class="login-logo">
            <img src="images/dict-logo.png" alt="DICT Logo"> <!-- Ensure path is correct -->
        </div>

        <h2 class="login-title">DICT SDN Provincial Office</h2>
        <p class="login-subtitle">Please log in to continue</p>

        <!-- Error Message Display -->
        <div class="error-message" id="errorMessage">
            <?php if (!empty($error_message)): ?>
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
            <?php endif; ?>
        </div>

        <form action="login.php" method="post" class="login-form" novalidate>
            <div class="form-group">
                <label for="username">Username or Email</label>
                <div class="input-group">
                    <i class="fas fa-user input-icon"></i>
                    <input type="text" id="username" name="username" value="<?php echo $username_value; ?>" placeholder="Enter your username or email" required autofocus>
                </div>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                 <div class="input-group">
                     <i class="fas fa-lock input-icon"></i>
                    <input type="password" id="password" name="password" placeholder="Enter your password" required>
                    <button type="button" class="password-toggle" id="passwordToggle" aria-label="Toggle password visibility">
                        <i class="fas fa-eye" id="passwordToggleIcon"></i>
                    </button>
                 </div>
            </div>
            <button type="submit" class="login-button">
                 <i class="fas fa-sign-in-alt"></i> Log In
            </button>
        </form>

        <div class="login-footer">
             <a href="#" id="resetPasswordLink">Reset Password</a>
             <div class="copyright">© <?php echo date("Y"); ?> DICT Surigao del Norte. All rights reserved.</div>
        </div>
    </div>

    <!-- Password Toggle Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password');
            const passwordToggle = document.getElementById('passwordToggle');
            const passwordToggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordToggle && passwordInput && passwordToggleIcon) {
                // Store original styles
                const originalStyles = {
                    borderColor: window.getComputedStyle(passwordInput).borderColor,
                    boxShadow: window.getComputedStyle(passwordInput).boxShadow,
                    backgroundColor: window.getComputedStyle(passwordInput).backgroundColor,
                    padding: window.getComputedStyle(passwordInput).padding,
                    fontSize: window.getComputedStyle(passwordInput).fontSize,
                    fontFamily: window.getComputedStyle(passwordInput).fontFamily
                };

                passwordToggle.addEventListener('click', function() {
                    // Toggle password visibility
                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        passwordToggleIcon.classList.remove('fa-eye');
                        passwordToggleIcon.classList.add('fa-eye-slash');
                    } else {
                        passwordInput.type = 'password';
                        passwordToggleIcon.classList.remove('fa-eye-slash');
                        passwordToggleIcon.classList.add('fa-eye');
                    }

                    // Restore original styles to ensure consistency
                    passwordInput.style.borderColor = originalStyles.borderColor;
                    passwordInput.style.boxShadow = originalStyles.boxShadow;
                    passwordInput.style.backgroundColor = originalStyles.backgroundColor;
                    passwordInput.style.padding = originalStyles.padding;
                    passwordInput.style.fontSize = originalStyles.fontSize;
                    passwordInput.style.fontFamily = originalStyles.fontFamily;

                    // Focus back on the input
                    passwordInput.focus();
                });

                // Prevent form submission when clicking the toggle button
                passwordToggle.addEventListener('mousedown', function(e) {
                    e.preventDefault();
                });
            }
        });
    </script>

    <!-- Reset Password Modal -->
    <div id="resetPasswordModal" class="modal small">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Reset Password</h2>
                <button type="button" class="close-modal" data-modal-id="resetPasswordModal">×</button>
            </div>
            <div class="modal-body">
                <div id="resetPasswordError" class="error-message" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i> <span id="resetPasswordErrorText"></span>
                </div>
                <p>Enter your reset token to continue. This token is provided by your system administrator.</p>
                <form id="resetTokenForm" method="post" novalidate>
                    <div class="form-group">
                        <label for="reset_token">Reset Token</label>
                        <div class="input-group">
                            <input type="text" id="reset_token" name="reset_token" placeholder="Enter your reset token" required>
                            <i class="fas fa-key input-icon-right"></i>
                        </div>
                    </div>
                    <button type="submit" id="validateTokenButton" class="login-button">
                        <i class="fas fa-check-circle"></i> Validate Token
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Reset Password Modal Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Reset Password Link
            const resetPasswordLink = document.getElementById('resetPasswordLink');
            const resetPasswordModal = document.getElementById('resetPasswordModal');
            const closeModalButtons = document.querySelectorAll('.close-modal');
            const resetTokenForm = document.getElementById('resetTokenForm');
            const resetPasswordError = document.getElementById('resetPasswordError');
            const resetPasswordErrorText = document.getElementById('resetPasswordErrorText');
            const validateTokenButton = document.getElementById('validateTokenButton');

            // Open Reset Password Modal
            if (resetPasswordLink) {
                resetPasswordLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (resetPasswordModal) {
                        resetPasswordModal.style.display = 'block';
                        document.getElementById('reset_token').focus();
                    }
                });
            }

            // Close Modal
            closeModalButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const modalId = this.getAttribute('data-modal-id');
                    const modal = document.getElementById(modalId);
                    if (modal) {
                        modal.style.display = 'none';
                        // Reset form and error messages
                        if (modalId === 'resetPasswordModal') {
                            resetTokenForm.reset();
                            resetPasswordError.style.display = 'none';
                        }
                    }
                });
            });

            // Close modal when clicking outside
            window.addEventListener('click', function(event) {
                if (event.target === resetPasswordModal) {
                    resetPasswordModal.style.display = 'none';
                    resetTokenForm.reset();
                    resetPasswordError.style.display = 'none';
                }
            });

            // Handle Reset Token Form Submission
            if (resetTokenForm) {
                resetTokenForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Get token value
                    const token = document.getElementById('reset_token').value.trim();

                    // Validate token
                    if (!token) {
                        resetPasswordError.style.display = 'block';
                        resetPasswordErrorText.textContent = 'Please enter your reset token.';
                        return;
                    }

                    // Disable button and show loading state
                    validateTokenButton.disabled = true;
                    validateTokenButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Validating...';

                    // Send token to server for validation
                    fetch('reset_password.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'action=validate_token&reset_token=' + encodeURIComponent(token)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Redirect to reset password page with token
                            window.location.href = 'reset_password.php?token=' + encodeURIComponent(token);
                        } else {
                            // Show error message
                            resetPasswordError.style.display = 'block';
                            resetPasswordErrorText.textContent = data.message || 'Invalid reset token. Please try again.';
                            validateTokenButton.disabled = false;
                            validateTokenButton.innerHTML = '<i class="fas fa-check-circle"></i> Validate Token';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        resetPasswordError.style.display = 'block';
                        resetPasswordErrorText.textContent = 'An error occurred. Please try again later.';
                        validateTokenButton.disabled = false;
                        validateTokenButton.innerHTML = '<i class="fas fa-check-circle"></i> Validate Token';
                    });
                });
            }
        });
    </script>
</body>
</html>